package aws

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/Laisky/errors/v2"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/relay/adaptor"
	claude "github.com/songquanpeng/one-api/relay/adaptor/aws/claude"
	llama3 "github.com/songquanpeng/one-api/relay/adaptor/aws/llama3"
	"github.com/songquanpeng/one-api/relay/adaptor/aws/utils"
	"github.com/songquanpeng/one-api/relay/billing/ratio"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/relaymode"
)

var _ adaptor.Adaptor = new(Adaptor)

type Adaptor struct {
	awsAdapter utils.AwsAdapter
	Config     aws.Config
	Meta       *meta.Meta
	AwsClient  *bedrockruntime.Client
}

func (a *Adaptor) Init(meta *meta.Meta) {
	a.Meta = meta
	defaultConfig, err := config.LoadDefaultConfig(context.Background(),
		config.WithRegion(meta.Config.Region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
			meta.Config.AK, meta.Config.SK, "")))
	if err != nil {
		return
	}
	a.Config = defaultConfig
	a.AwsClient = bedrockruntime.NewFromConfig(defaultConfig)
}

func (a *Adaptor) ConvertRequest(c *gin.Context, relayMode int, request *model.GeneralOpenAIRequest) (any, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	adaptor := GetAdaptor(request.Model)
	if adaptor == nil {
		return nil, errors.New("adaptor not found")
	}

	a.awsAdapter = adaptor
	return adaptor.ConvertRequest(c, relayMode, request)
}

func (a *Adaptor) DoResponse(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, err *model.ErrorWithStatusCode) {
	if a.awsAdapter == nil {
		return nil, utils.WrapErr(errors.New("awsAdapter is nil"))
	}
	return a.awsAdapter.DoResponse(c, a.AwsClient, meta)
}

func (a *Adaptor) GetModelList() (models []string) {
	for model := range adaptors {
		models = append(models, model)
	}
	return
}

func (a *Adaptor) GetChannelName() string {
	return "aws"
}

func (a *Adaptor) GetRequestURL(meta *meta.Meta) (string, error) {
	return "", nil
}

func (a *Adaptor) SetupRequestHeader(c *gin.Context, req *http.Request, meta *meta.Meta) error {
	return nil
}

func (a *Adaptor) ConvertImageRequest(_ *gin.Context, request *model.ImageRequest) (any, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}
	return request, nil
}

func (a *Adaptor) ConvertClaudeRequest(c *gin.Context, request *model.ClaudeRequest) (any, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	// AWS Bedrock supports Claude models natively
	// Get the appropriate sub-adaptor for the model
	adaptor := GetAdaptor(request.Model)
	if adaptor == nil {
		return nil, errors.New("adaptor not found for model: " + request.Model)
	}

	// Convert Claude request to OpenAI format first, then let the sub-adaptor handle it
	openaiRequest := &model.GeneralOpenAIRequest{
		Model:       request.Model,
		MaxTokens:   request.MaxTokens,
		Temperature: request.Temperature,
		TopP:        request.TopP,
		Stream:      request.Stream != nil && *request.Stream,
		Stop:        request.StopSequences,
	}

	// Convert system prompt
	if request.System != nil {
		switch system := request.System.(type) {
		case string:
			if system != "" {
				openaiRequest.Messages = append(openaiRequest.Messages, model.Message{
					Role:    "system",
					Content: system,
				})
			}
		case []any:
			// For structured system content, extract text parts
			var systemParts []string
			for _, block := range system {
				if blockMap, ok := block.(map[string]any); ok {
					if text, exists := blockMap["text"]; exists {
						if textStr, ok := text.(string); ok {
							systemParts = append(systemParts, textStr)
						}
					}
				}
			}
			if len(systemParts) > 0 {
				systemText := strings.Join(systemParts, "\n")
				openaiRequest.Messages = append(openaiRequest.Messages, model.Message{
					Role:    "system",
					Content: systemText,
				})
			}
		}
	}

	// Convert messages
	for _, msg := range request.Messages {
		openaiMessage := model.Message{
			Role: msg.Role,
		}

		// Convert content based on type
		switch content := msg.Content.(type) {
		case string:
			// Simple string content
			openaiMessage.Content = content
		case []any:
			// Structured content blocks - convert to OpenAI format
			var contentParts []model.MessageContent
			for _, block := range content {
				if blockMap, ok := block.(map[string]any); ok {
					if blockType, exists := blockMap["type"]; exists {
						switch blockType {
						case "text":
							if text, exists := blockMap["text"]; exists {
								if textStr, ok := text.(string); ok {
									contentParts = append(contentParts, model.MessageContent{
										Type: "text",
										Text: &textStr,
									})
								}
							}
						case "image":
							if source, exists := blockMap["source"]; exists {
								if sourceMap, ok := source.(map[string]any); ok {
									imageURL := model.ImageURL{}
									if mediaType, exists := sourceMap["media_type"]; exists {
										if data, exists := sourceMap["data"]; exists {
											if dataStr, ok := data.(string); ok {
												// Convert to data URL format
												imageURL.Url = fmt.Sprintf("data:%s;base64,%s", mediaType, dataStr)
											}
										}
									}
									contentParts = append(contentParts, model.MessageContent{
										Type:     "image_url",
										ImageURL: &imageURL,
									})
								}
							}
						}
					}
				}
			}
			if len(contentParts) > 0 {
				openaiMessage.Content = contentParts
			}
		default:
			// Fallback: convert to string
			if contentBytes, err := json.Marshal(content); err == nil {
				openaiMessage.Content = string(contentBytes)
			}
		}

		openaiRequest.Messages = append(openaiRequest.Messages, openaiMessage)
	}

	// Convert tools
	for _, tool := range request.Tools {
		openaiTool := model.Tool{
			Type: "function",
			Function: model.Function{
				Name:        tool.Name,
				Description: tool.Description,
			},
		}

		// Convert input schema
		if tool.InputSchema != nil {
			if schemaMap, ok := tool.InputSchema.(map[string]any); ok {
				openaiTool.Function.Parameters = schemaMap
			}
		}

		openaiRequest.Tools = append(openaiRequest.Tools, openaiTool)
	}

	// Convert tool choice
	if request.ToolChoice != nil {
		openaiRequest.ToolChoice = request.ToolChoice
	}

	// Mark this as a Claude Messages conversion for response handling
	c.Set(ctxkey.ClaudeMessagesConversion, true)
	c.Set(ctxkey.OriginalClaudeRequest, request)

	// Store the sub-adaptor for later use
	a.awsAdapter = adaptor

	// Now convert using the sub-adaptor's logic
	return adaptor.ConvertRequest(c, relaymode.ChatCompletions, openaiRequest)
}

func (a *Adaptor) DoRequest(c *gin.Context, meta *meta.Meta, requestBody io.Reader) (*http.Response, error) {
	return nil, nil
}

// Pricing methods - AWS adapter delegates pricing to child adapters (DRY principle)
// This eliminates duplicate pricing definitions and ensures consistency
func (a *Adaptor) GetDefaultModelPricing() map[string]adaptor.ModelConfig {
	// Merge pricing from all child adapters to create unified AWS pricing
	mergedPricing := make(map[string]adaptor.ModelConfig)

	// Import Claude pricing from AWS Claude sub-adapter
	for model, config := range claude.ModelRatios {
		mergedPricing[model] = config
	}

	// Import Llama3 pricing from AWS Llama3 sub-adapter
	for model, config := range llama3.ModelRatios {
		mergedPricing[model] = config
	}

	// TODO: Add other AWS sub-adapters (Titan, Cohere, AI21, Mistral) when they are refactored
	// For now, keep some hard-coded pricing for models not yet moved to sub-adapters
	const MilliTokensUsd = ratio.MilliTokensUsd

	// Amazon Nova Models (TODO: move to sub-adapter)
	mergedPricing["amazon-nova-micro"] = adaptor.ModelConfig{Ratio: 0.035 * MilliTokensUsd, CompletionRatio: 4.28}
	mergedPricing["amazon-nova-lite"] = adaptor.ModelConfig{Ratio: 0.06 * MilliTokensUsd, CompletionRatio: 4.17}
	mergedPricing["amazon-nova-pro"] = adaptor.ModelConfig{Ratio: 0.8 * MilliTokensUsd, CompletionRatio: 4}
	mergedPricing["amazon-nova-premier"] = adaptor.ModelConfig{Ratio: 2.4 * MilliTokensUsd, CompletionRatio: 4.17}

	// Titan Models (TODO: move to sub-adapter)
	mergedPricing["amazon-titan-text-lite"] = adaptor.ModelConfig{Ratio: 0.3 * MilliTokensUsd, CompletionRatio: 1.33}
	mergedPricing["amazon-titan-text-express"] = adaptor.ModelConfig{Ratio: 0.8 * MilliTokensUsd, CompletionRatio: 2}
	mergedPricing["amazon-titan-embed-text"] = adaptor.ModelConfig{Ratio: 0.1 * MilliTokensUsd, CompletionRatio: 1}

	// Cohere Models (TODO: move to sub-adapter)
	mergedPricing["cohere-command-text"] = adaptor.ModelConfig{Ratio: 1.5 * MilliTokensUsd, CompletionRatio: 1.33}
	mergedPricing["cohere-command-light-text"] = adaptor.ModelConfig{Ratio: 0.3 * MilliTokensUsd, CompletionRatio: 2}

	// AI21 Models (TODO: move to sub-adapter)
	mergedPricing["ai21-j2-mid"] = adaptor.ModelConfig{Ratio: 12.5 * MilliTokensUsd, CompletionRatio: 1}
	mergedPricing["ai21-j2-ultra"] = adaptor.ModelConfig{Ratio: 18.8 * MilliTokensUsd, CompletionRatio: 1}
	mergedPricing["ai21-jamba-1.5"] = adaptor.ModelConfig{Ratio: 2 * MilliTokensUsd, CompletionRatio: 4}

	// Mistral Models (TODO: move to sub-adapter)
	mergedPricing["mistral-7b-instruct"] = adaptor.ModelConfig{Ratio: 0.15 * MilliTokensUsd, CompletionRatio: 1.33}
	mergedPricing["mistral-8x7b-instruct"] = adaptor.ModelConfig{Ratio: 0.45 * MilliTokensUsd, CompletionRatio: 1.56}
	mergedPricing["mistral-large"] = adaptor.ModelConfig{Ratio: 4 * MilliTokensUsd, CompletionRatio: 3}

	return mergedPricing
}

func (a *Adaptor) GetModelRatio(modelName string) float64 {
	// Delegate pricing to the appropriate child adapter based on model type
	childAdapter := GetAdaptor(modelName)
	if childAdapter != nil {
		// Try to get pricing from child adapter if it has pricing methods
		if pricingAdapter, ok := childAdapter.(interface{ GetModelRatio(string) float64 }); ok {
			return pricingAdapter.GetModelRatio(modelName)
		}
	}

	// Fallback to hard-coded pricing for models not handled by child adapters
	pricing := a.GetDefaultModelPricing()
	if price, exists := pricing[modelName]; exists {
		return price.Ratio
	}
	// Default AWS pricing (Claude-like)
	return 3 * 0.000001 // Default USD pricing
}

func (a *Adaptor) GetCompletionRatio(modelName string) float64 {
	// Delegate pricing to the appropriate child adapter based on model type
	childAdapter := GetAdaptor(modelName)
	if childAdapter != nil {
		// Try to get pricing from child adapter if it has pricing methods
		if pricingAdapter, ok := childAdapter.(interface{ GetCompletionRatio(string) float64 }); ok {
			return pricingAdapter.GetCompletionRatio(modelName)
		}
	}

	// Fallback to hard-coded pricing for models not handled by child adapters
	pricing := a.GetDefaultModelPricing()
	if price, exists := pricing[modelName]; exists {
		return price.CompletionRatio
	}
	// Default completion ratio for AWS
	return 5.0
}
