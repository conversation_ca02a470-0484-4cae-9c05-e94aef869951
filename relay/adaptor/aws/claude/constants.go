package aws

import (
	"github.com/songquanpeng/one-api/relay/adaptor"
	"github.com/songquanpeng/one-api/relay/adaptor/anthropic"
	"github.com/songquanpeng/one-api/relay/billing/ratio"
)

// ModelRatios imports Claude pricing from the Anthropic adapter (DRY principle)
// AWS Bedrock Claude models use the same pricing structure as Anthropic Claude
// but with AWS Bedrock-specific pricing rates
// Pricing from https://aws.amazon.com/bedrock/pricing/
var ModelRatios = func() map[string]adaptor.ModelConfig {
	// Import base Claude pricing from Anthropic adapter
	basePricing := anthropic.ModelRatios

	// AWS Bedrock Claude pricing (different from direct Anthropic pricing)
	const MilliTokensUsd = ratio.MilliTokensUsd

	awsPricing := make(map[string]adaptor.ModelConfig)

	// AWS Bedrock Claude models with AWS-specific pricing
	awsPricing["claude-instant-1.2"] = adaptor.ModelConfig{
		Ratio:           0.8 * MilliTokensUsd, // $0.8 per 1K tokens
		CompletionRatio: 3.125,                // $2.5 per 1K tokens (2.5/0.8 = 3.125)
	}
	awsPricing["claude-2.0"] = adaptor.ModelConfig{
		Ratio:           8 * MilliTokensUsd, // $8 per 1K tokens
		CompletionRatio: 3.125,              // $25 per 1K tokens (25/8 = 3.125)
	}
	awsPricing["claude-2.1"] = adaptor.ModelConfig{
		Ratio:           8 * MilliTokensUsd, // $8 per 1K tokens
		CompletionRatio: 3.125,              // $25 per 1K tokens (25/8 = 3.125)
	}
	awsPricing["claude-3-haiku-20240307"] = adaptor.ModelConfig{
		Ratio:           0.25 * MilliTokensUsd, // $0.25 per 1K tokens
		CompletionRatio: 5,                     // $1.25 per 1K tokens (1.25/0.25 = 5)
	}
	awsPricing["claude-3-sonnet-20240229"] = adaptor.ModelConfig{
		Ratio:           3 * MilliTokensUsd, // $3 per 1K tokens
		CompletionRatio: 5,                  // $15 per 1K tokens (15/3 = 5)
	}
	awsPricing["claude-3-opus-20240229"] = adaptor.ModelConfig{
		Ratio:           15 * MilliTokensUsd, // $15 per 1K tokens
		CompletionRatio: 5,                   // $75 per 1K tokens (75/15 = 5)
	}
	awsPricing["claude-3-5-sonnet-20240620"] = adaptor.ModelConfig{
		Ratio:           3 * MilliTokensUsd, // $3 per 1K tokens
		CompletionRatio: 5,                  // $15 per 1K tokens (15/3 = 5)
	}
	awsPricing["claude-3-5-sonnet-20241022"] = adaptor.ModelConfig{
		Ratio:           3 * MilliTokensUsd, // $3 per 1K tokens
		CompletionRatio: 5,                  // $15 per 1K tokens (15/3 = 5)
	}
	awsPricing["claude-3-5-haiku-20241022"] = adaptor.ModelConfig{
		Ratio:           0.25 * MilliTokensUsd, // $0.25 per 1K tokens
		CompletionRatio: 5,                     // $1.25 per 1K tokens (1.25/0.25 = 5)
	}

	// For models that exist in both AWS and Anthropic, prefer AWS pricing
	// For models that only exist in Anthropic, use Anthropic pricing as fallback
	for model, config := range basePricing {
		if _, exists := awsPricing[model]; !exists {
			// Use Anthropic pricing as fallback for models not specifically priced for AWS
			awsPricing[model] = config
		}
	}

	return awsPricing
}()

// AwsModelIDMap maps model names to AWS Bedrock model IDs
var AwsModelIDMap = map[string]string{
	"claude-instant-1.2":           "anthropic.claude-instant-v1",
	"claude-2.0":                   "anthropic.claude-v2",
	"claude-2.1":                   "anthropic.claude-v2:1",
	"claude-3-haiku-20240307":      "anthropic.claude-3-haiku-20240307-v1:0",
	"claude-3-sonnet-20240229":     "anthropic.claude-3-sonnet-20240229-v1:0",
	"claude-3-opus-20240229":       "anthropic.claude-3-opus-20240229-v1:0",
	"claude-opus-4-20250514":       "anthropic.claude-opus-4-20250514-v1:0",
	"claude-3-5-sonnet-20240620":   "anthropic.claude-3-5-sonnet-20240620-v1:0",
	"claude-3-5-sonnet-20241022":   "anthropic.claude-3-5-sonnet-20241022-v2:0",
	"claude-3-5-sonnet-latest":     "anthropic.claude-3-5-sonnet-20241022-v2:0",
	"claude-3-5-haiku-20241022":    "anthropic.claude-3-5-haiku-20241022-v1:0",
	"claude-3-7-sonnet-latest":     "anthropic.claude-3-7-sonnet-20250219-v1:0",
	"claude-3-7-sonnet-20250219":   "anthropic.claude-3-7-sonnet-20250219-v1:0",
	"claude-sonnet-4-20250514":     "anthropic.claude-sonnet-4-20250514-v1:0",
	"claude-3-7-sonnet-latest-tag": "claude-3-7-sonnet-latest-tag",
	"claude-4-sonnet-latest-tag":   "claude-4-sonnet-latest-tag",
}

// ModelList derived from ModelRatios for backward compatibility
var ModelList = adaptor.GetModelListFromPricing(ModelRatios)
