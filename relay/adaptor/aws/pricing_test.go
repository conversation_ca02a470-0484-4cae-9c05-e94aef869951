package aws

import (
	"testing"
)

func TestAWSPricing(t *testing.T) {
	adapter := &Adaptor{}

	t.Run("Claude_Model_Pricing", func(t *testing.T) {
		modelName := "claude-3-sonnet-20240229"

		// Test model ratio
		modelRatio := adapter.GetModelRatio(modelName)
		expectedRatio := 3 * 0.000001 // $3 per 1K tokens

		if modelRatio != expectedRatio {
			t.<PERSON>rrorf("Expected model ratio %f, got %f", expectedRatio, modelRatio)
		}

		// Test completion ratio
		completionRatio := adapter.GetCompletionRatio(modelName)
		expectedCompletionRatio := 5.0 // $15 per 1K tokens (15/3 = 5)

		if completionRatio != expectedCompletionRatio {
			t.<PERSON>rrorf("Expected completion ratio %f, got %f", expectedCompletionRatio, completionRatio)
		}

		t.Logf("✓ Claude 3 Sonnet pricing: model_ratio=%f, completion_ratio=%f", modelRatio, completionRatio)
	})

	t.Run("Llama3_Model_Pricing", func(t *testing.T) {
		modelName := "llama3-8b-instruct-v1:0"

		// Test model ratio
		modelRatio := adapter.GetModelRatio(modelName)
		expectedRatio := 0.3 * 0.000001 // $0.3 per 1K tokens

		if modelRatio != expectedRatio {
			t.Errorf("Expected model ratio %f, got %f", expectedRatio, modelRatio)
		}

		// Test completion ratio
		completionRatio := adapter.GetCompletionRatio(modelName)
		expectedCompletionRatio := 2.0 // $0.6 per 1K tokens (0.6/0.3 = 2)

		if completionRatio != expectedCompletionRatio {
			t.Errorf("Expected completion ratio %f, got %f", expectedCompletionRatio, completionRatio)
		}

		t.Logf("✓ Llama3 8B pricing: model_ratio=%f, completion_ratio=%f", modelRatio, completionRatio)
	})

	t.Run("Child_Adapter_Delegation", func(t *testing.T) {
		testModels := []struct {
			model    string
			expected string
		}{
			{"claude-3-sonnet-20240229", "Claude"},
			{"llama3-8b-instruct-v1:0", "Llama3"},
		}

		for _, test := range testModels {
			// Test child adapter
			childAdapter := GetAdaptor(test.model)
			if childAdapter == nil {
				t.Errorf("Child adapter not found for %s", test.model)
				continue
			}

			// Check if child adapter implements pricing methods
			if pricingAdapter, ok := childAdapter.(interface{ GetModelRatio(string) float64 }); ok {
				childRatio := pricingAdapter.GetModelRatio(test.model)
				mainRatio := adapter.GetModelRatio(test.model)

				if childRatio != mainRatio {
					t.Errorf("%s: Child adapter ratio %f != main adapter ratio %f", test.model, childRatio, mainRatio)
				}

				t.Logf("✓ %s child adapter delegation works: model_ratio=%f", test.expected, childRatio)
			} else {
				t.Errorf("Child adapter for %s does not implement GetModelRatio interface", test.model)
			}
		}
	})

	t.Run("Pricing_Consistency", func(t *testing.T) {
		// Test that pricing methods are consistent
		defaultPricing := adapter.GetDefaultModelPricing()
		if len(defaultPricing) == 0 {
			t.Fatal("No default pricing found")
		}

		// Test a few models for consistency
		testModels := []string{
			"claude-3-sonnet-20240229",
			"llama3-8b-instruct-v1:0",
		}

		for _, modelName := range testModels {
			// Check that GetModelRatio matches the pricing map
			expectedConfig, exists := defaultPricing[modelName]
			if !exists {
				t.Errorf("Model %s not found in default pricing", modelName)
				continue
			}

			actualRatio := adapter.GetModelRatio(modelName)
			if actualRatio != expectedConfig.Ratio {
				t.Errorf("Model %s: GetModelRatio()=%f != DefaultPricing.Ratio=%f",
					modelName, actualRatio, expectedConfig.Ratio)
			}

			actualCompletionRatio := adapter.GetCompletionRatio(modelName)
			if actualCompletionRatio != expectedConfig.CompletionRatio {
				t.Errorf("Model %s: GetCompletionRatio()=%f != DefaultPricing.CompletionRatio=%f",
					modelName, actualCompletionRatio, expectedConfig.CompletionRatio)
			}

			t.Logf("✓ %s pricing consistency verified", modelName)
		}
	})

	t.Run("DRY_Principle_Verification", func(t *testing.T) {
		// Verify that pricing is imported from child adapters, not duplicated
		defaultPricing := adapter.GetDefaultModelPricing()

		// Count models from each child adapter
		claudeModels := 0
		llamaModels := 0

		for modelName := range defaultPricing {
			childAdapter := GetAdaptor(modelName)
			if childAdapter != nil {
				switch childAdapter.(type) {
				case interface{ GetModelRatio(string) float64 }:
					// This is a properly implemented child adapter
					if GetAdaptor(modelName) != nil {
						// Check which type of adapter it is
						adaptorType := GetAdaptor(modelName)
						if adaptorType != nil {
							// Count by model name patterns (simple heuristic)
							if len(modelName) > 6 && modelName[:6] == "claude" {
								claudeModels++
							} else if len(modelName) > 5 && modelName[:5] == "llama" {
								llamaModels++
							}
						}
					}
				}
			}
		}

		t.Logf("✓ DRY principle: Found %d Claude models, %d Llama models in merged pricing", claudeModels, llamaModels)

		if claudeModels == 0 {
			t.Error("No Claude models found - child adapter delegation may not be working")
		}
		if llamaModels == 0 {
			t.Error("No Llama models found - child adapter delegation may not be working")
		}
	})
}
