package aws

import (
	"github.com/songquanpeng/one-api/relay/adaptor"
)

// ModelRatios contains AWS Bedrock Llama3 model pricing
// Pricing from https://aws.amazon.com/bedrock/pricing/
var ModelRatios = map[string]adaptor.ModelConfig{
	// Legacy model names (for backward compatibility)
	"llama3-8b-8192": {
		Ratio:           0.3 * 0.000001, // $0.3 per 1K tokens
		CompletionRatio: 2.0,            // $0.6 per 1K tokens (0.6/0.3 = 2.0)
	},
	"llama3-70b-8192": {
		Ratio:           2.65 * 0.000001, // $2.65 per 1K tokens
		CompletionRatio: 1.32,            // $3.5 per 1K tokens (3.5/2.65 = 1.32)
	},

	// AWS Bedrock Llama3 models
	"llama3-8b-instruct-v1:0": {
		Ratio:           0.3 * 0.000001, // $0.3 per 1K tokens
		CompletionRatio: 2.0,            // $0.6 per 1K tokens (0.6/0.3 = 2.0)
	},
	"llama3-70b-instruct-v1:0": {
		Ratio:           2.65 * 0.000001, // $2.65 per 1K tokens
		CompletionRatio: 1.32,            // $3.5 per 1K tokens (3.5/2.65 = 1.32)
	},
	"llama3.1-8b-instruct-v1:0": {
		Ratio:           0.22 * 0.000001, // $0.22 per 1K tokens
		CompletionRatio: 2.27,            // $0.5 per 1K tokens (0.5/0.22 = 2.27)
	},
	"llama3.1-70b-instruct-v1:0": {
		Ratio:           0.99 * 0.000001, // $0.99 per 1K tokens
		CompletionRatio: 3.03,            // $3.0 per 1K tokens (3.0/0.99 = 3.03)
	},
	"llama3.1-405b-instruct-v1:0": {
		Ratio:           5.32 * 0.000001, // $5.32 per 1K tokens
		CompletionRatio: 3.01,            // $16.0 per 1K tokens (16.0/5.32 = 3.01)
	},
	"llama3.2-1b-instruct-v1:0": {
		Ratio:           0.1 * 0.000001, // $0.1 per 1K tokens
		CompletionRatio: 4.0,            // $0.4 per 1K tokens (0.4/0.1 = 4.0)
	},
	"llama3.2-3b-instruct-v1:0": {
		Ratio:           0.15 * 0.000001, // $0.15 per 1K tokens
		CompletionRatio: 4.0,             // $0.6 per 1K tokens (0.6/0.15 = 4.0)
	},
	"llama3.2-11b-instruct-v1:0": {
		Ratio:           0.35 * 0.000001, // $0.35 per 1K tokens
		CompletionRatio: 4.0,             // $1.4 per 1K tokens (1.4/0.35 = 4.0)
	},
	"llama3.2-90b-instruct-v1:0": {
		Ratio:           1.65 * 0.000001, // $1.65 per 1K tokens
		CompletionRatio: 3.64,            // $6.0 per 1K tokens (6.0/1.65 = 3.64)
	},
}

// AwsModelIDMap maps model names to AWS Bedrock model IDs
var AwsModelIDMap = map[string]string{
	// Legacy model names (for backward compatibility)
	"llama3-8b-8192":  "meta.llama3-8b-instruct-v1:0",
	"llama3-70b-8192": "meta.llama3-70b-instruct-v1:0",

	// Standard AWS Bedrock model names
	"llama3-8b-instruct-v1:0":     "meta.llama3-8b-instruct-v1:0",
	"llama3-70b-instruct-v1:0":    "meta.llama3-70b-instruct-v1:0",
	"llama3.1-8b-instruct-v1:0":   "meta.llama3-1-8b-instruct-v1:0",
	"llama3.1-70b-instruct-v1:0":  "meta.llama3-1-70b-instruct-v1:0",
	"llama3.1-405b-instruct-v1:0": "meta.llama3-1-405b-instruct-v1:0",
	"llama3.2-1b-instruct-v1:0":   "meta.llama3-2-1b-instruct-v1:0",
	"llama3.2-3b-instruct-v1:0":   "meta.llama3-2-3b-instruct-v1:0",
	"llama3.2-11b-instruct-v1:0":  "meta.llama3-2-11b-instruct-v1:0",
	"llama3.2-90b-instruct-v1:0":  "meta.llama3-2-90b-instruct-v1:0",
}

// ModelList derived from ModelRatios for backward compatibility
var ModelList = adaptor.GetModelListFromPricing(ModelRatios)
